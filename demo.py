#!/usr/bin/env python3
"""
Demo script cho Video Renderer Tool
"""

import os
import subprocess
import time


def print_header(title):
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)


def print_step(step, description):
    print(f"\n🔸 Bước {step}: {description}")


def run_command(cmd, description=""):
    print(f"\n💻 Chạy: {cmd}")
    if description:
        print(f"   {description}")
    
    start_time = time.time()
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"   ⏱️  Thời gian: {end_time - start_time:.2f} giây")
    
    if result.returncode == 0:
        print("   ✅ Thành công!")
        if result.stdout:
            print(f"   📄 Output: {result.stdout.strip()}")
    else:
        print("   ❌ Lỗi!")
        if result.stderr:
            print(f"   🚨 Error: {result.stderr.strip()}")
    
    return result.returncode == 0


def check_file_exists(filename):
    if os.path.exists(filename):
        size = os.path.getsize(filename)
        size_mb = size / (1024 * 1024)
        print(f"   📁 File: {filename} ({size_mb:.2f} MB)")
        return True
    else:
        print(f"   ❌ File không tồn tại: {filename}")
        return False


def main():
    print_header("🎬 DEMO VIDEO RENDERER TOOL")
    
    print("Tool này có thể:")
    print("✅ Kết hợp ảnh + audio thành video")
    print("✅ Hỗ trợ video dài (3+ tiếng)")
    print("✅ Tự động resize ảnh")
    print("✅ Giao diện CLI và GUI")
    print("✅ Nhiều định dạng file")
    
    # Bước 1: Kiểm tra dependencies
    print_step(1, "Kiểm tra dependencies")
    
    success = run_command("python -c \"import moviepy; print('MoviePy:', moviepy.__version__)\"", 
                         "Kiểm tra MoviePy")
    if not success:
        print("❌ Vui lòng cài đặt dependencies: pip install -r requirements.txt")
        return
    
    success = run_command("python -c \"from PIL import Image; print('Pillow: OK')\"", 
                         "Kiểm tra Pillow")
    if not success:
        print("❌ Vui lòng cài đặt Pillow")
        return
    
    # Bước 2: Tạo file test
    print_step(2, "Tạo file test")
    
    if not os.path.exists("test_image.jpg"):
        run_command("python create_test_files.py", "Tạo ảnh và audio test")
    
    check_file_exists("test_image.jpg")
    check_file_exists("test_audio.wav")
    
    # Bước 3: Demo CLI - Video ngắn
    print_step(3, "Demo CLI - Video ngắn (10 giây)")
    
    success = run_command("python video_renderer.py test_image.jpg test_audio.wav -o demo_short.mp4",
                         "Render video ngắn")
    
    if success:
        check_file_exists("demo_short.mp4")
    
    # Bước 4: Demo các tùy chọn
    print_step(4, "Demo các tùy chọn khác nhau")
    
    # HD 720p
    success = run_command("python video_renderer.py test_image.jpg test_audio.wav -o demo_hd.mp4 --width 1280 --height 720",
                         "Render video HD 720p")
    
    if success:
        check_file_exists("demo_hd.mp4")
    
    # Bước 5: Tạo audio dài (tùy chọn)
    print_step(5, "Demo video dài (tùy chọn)")
    
    print("⚠️  Video dài sẽ mất nhiều thời gian render!")
    user_input = input("Bạn có muốn tạo video 3 phút? (y/N): ").lower().strip()
    
    if user_input == 'y':
        if not os.path.exists("long_audio_3min.wav"):
            run_command("python create_long_audio.py", "Tạo audio 3 phút")
        
        if check_file_exists("long_audio_3min.wav"):
            print("🚀 Bắt đầu render video 3 phút...")
            print("   (Quá trình này có thể mất 5-15 phút)")
            
            success = run_command("python video_renderer.py test_image.jpg long_audio_3min.wav -o demo_long.mp4",
                                 "Render video 3 phút")
            
            if success:
                check_file_exists("demo_long.mp4")
    else:
        print("⏭️  Bỏ qua demo video dài")
    
    # Bước 6: Demo GUI
    print_step(6, "Demo GUI")
    
    print("🖥️  Mở giao diện GUI...")
    print("   (Cửa sổ GUI sẽ mở trong vài giây)")
    print("   Bạn có thể:")
    print("   - Chọn file ảnh và audio")
    print("   - Tùy chỉnh kích thước")
    print("   - Theo dõi tiến trình render")
    
    user_input = input("Bạn có muốn mở GUI? (y/N): ").lower().strip()
    
    if user_input == 'y':
        print("🚀 Mở GUI...")
        subprocess.Popen(["python", "gui_video_renderer.py"])
        print("   GUI đã được mở trong cửa sổ riêng")
    else:
        print("⏭️  Bỏ qua demo GUI")
    
    # Bước 7: Chạy tests
    print_step(7, "Chạy tests")
    
    user_input = input("Bạn có muốn chạy unit tests? (y/N): ").lower().strip()
    
    if user_input == 'y':
        run_command("python test_video_renderer.py", "Chạy unit tests")
    else:
        print("⏭️  Bỏ qua tests")
    
    # Tổng kết
    print_header("🎉 DEMO HOÀN THÀNH")
    
    print("📁 Các file đã tạo:")
    demo_files = ["demo_short.mp4", "demo_hd.mp4", "demo_long.mp4"]
    
    for file in demo_files:
        if os.path.exists(file):
            check_file_exists(file)
    
    print("\n🚀 Cách sử dụng:")
    print("   CLI: python video_renderer.py <image> <audio> -o <output>")
    print("   GUI: python gui_video_renderer.py")
    
    print("\n📖 Xem thêm: README.md")
    print("🐛 Báo lỗi: Kiểm tra file test_video_renderer.py")
    
    print("\n✨ Tool sẵn sàng sử dụng!")


if __name__ == "__main__":
    main()
