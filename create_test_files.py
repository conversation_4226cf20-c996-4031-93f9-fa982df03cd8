#!/usr/bin/env python3
"""
Script để tạo file test cho video renderer
"""

from PIL import Image, ImageDraw, ImageFont
import numpy as np
import os


def create_test_image():
    """Tạo ảnh test đẹp"""
    # Tạo ảnh 1920x1080 với gradient màu
    width, height = 1920, 1080
    
    # Tạo gradient từ xanh dương đến tím
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    # Vẽ gradient nền
    for y in range(height):
        # Tính toán màu gradient
        ratio = y / height
        r = int(50 + ratio * 100)  # 50 -> 150
        g = int(100 + ratio * 50)  # 100 -> 150  
        b = int(200 + ratio * 55)  # 200 -> 255
        
        color = (r, g, b)
        draw.line([(0, y), (width, y)], fill=color)
    
    # Vẽ một số hình trang trí
    # Vòng tròn lớn ở giữa
    center_x, center_y = width // 2, height // 2
    circle_radius = 200
    
    # Vẽ vòng tròn với viền
    draw.ellipse([
        center_x - circle_radius, center_y - circle_radius,
        center_x + circle_radius, center_y + circle_radius
    ], outline='white', width=8)
    
    # Vẽ vòng tròn nhỏ hơn bên trong
    inner_radius = 150
    draw.ellipse([
        center_x - inner_radius, center_y - inner_radius,
        center_x + inner_radius, center_y + inner_radius
    ], fill='rgba(255,255,255,100)', outline='white', width=4)
    
    # Thêm text
    try:
        # Thử sử dụng font hệ thống
        font_large = ImageFont.truetype("arial.ttf", 60)
        font_small = ImageFont.truetype("arial.ttf", 30)
    except:
        # Fallback nếu không tìm thấy font
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # Text chính
    main_text = "VIDEO RENDERER"
    text_bbox = draw.textbbox((0, 0), main_text, font=font_large)
    text_width = text_bbox[2] - text_bbox[0]
    text_x = (width - text_width) // 2
    text_y = center_y - 100
    
    # Vẽ shadow cho text
    draw.text((text_x + 3, text_y + 3), main_text, fill='black', font=font_large)
    draw.text((text_x, text_y), main_text, fill='white', font=font_large)
    
    # Text phụ
    sub_text = "Test Image for Audio + Video Rendering"
    sub_bbox = draw.textbbox((0, 0), sub_text, font=font_small)
    sub_width = sub_bbox[2] - sub_bbox[0]
    sub_x = (width - sub_width) // 2
    sub_y = center_y + 120
    
    draw.text((sub_x + 2, sub_y + 2), sub_text, fill='black', font=font_small)
    draw.text((sub_x, sub_y), sub_text, fill='white', font=font_small)
    
    # Lưu ảnh
    image.save('test_image.jpg', 'JPEG', quality=95)
    print("✅ Đã tạo test_image.jpg")


def create_test_audio():
    """Tạo file audio test ngắn (sine wave)"""
    try:
        import wave
        import struct
        import math
        
        # Tham số audio
        sample_rate = 44100  # Hz
        duration = 10  # giây
        frequency = 440  # Hz (note A4)
        
        # Tạo sine wave
        frames = []
        for i in range(int(sample_rate * duration)):
            # Tạo sine wave với fade in/out
            t = i / sample_rate
            fade_duration = 1.0  # 1 giây fade
            
            if t < fade_duration:
                amplitude = t / fade_duration
            elif t > duration - fade_duration:
                amplitude = (duration - t) / fade_duration
            else:
                amplitude = 1.0
            
            # Tạo âm thanh với nhiều tần số (chord)
            value = 0
            value += 0.3 * math.sin(2 * math.pi * frequency * t)  # A4
            value += 0.2 * math.sin(2 * math.pi * frequency * 1.25 * t)  # C#5
            value += 0.2 * math.sin(2 * math.pi * frequency * 1.5 * t)  # E5
            
            value *= amplitude * 0.3  # Giảm âm lượng
            
            # Chuyển đổi sang 16-bit
            frames.append(struct.pack('<h', int(value * 32767)))
        
        # Lưu file WAV
        with wave.open('test_audio.wav', 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(b''.join(frames))
        
        print("✅ Đã tạo test_audio.wav (10 giây)")
        
    except Exception as e:
        print(f"❌ Không thể tạo file audio: {e}")
        print("💡 Bạn có thể sử dụng file audio có sẵn thay thế")


def main():
    print("🎨 Đang tạo file test...")
    
    # Tạo ảnh test
    create_test_image()
    
    # Tạo audio test
    create_test_audio()
    
    print("\n📁 Các file test đã được tạo:")
    if os.path.exists('test_image.jpg'):
        print(f"  - test_image.jpg ({os.path.getsize('test_image.jpg')} bytes)")
    if os.path.exists('test_audio.wav'):
        print(f"  - test_audio.wav ({os.path.getsize('test_audio.wav')} bytes)")
    
    print("\n🚀 Bây giờ bạn có thể chạy:")
    print("  python video_renderer.py test_image.jpg test_audio.wav")
    print("  hoặc")
    print("  python gui_video_renderer.py")


if __name__ == "__main__":
    main()
