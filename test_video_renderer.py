#!/usr/bin/env python3
"""
Test script cho Video Renderer Tool
"""

import unittest
import os
import tempfile
from unittest.mock import patch, MagicMock
from video_renderer import VideoRenderer
from PIL import Image
import numpy as np


class TestVideoRenderer(unittest.TestCase):
    
    def setUp(self):
        self.renderer = VideoRenderer()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        # Cleanup temp files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_image(self, filename, width=800, height=600):
        """Tạo file ảnh test"""
        image_path = os.path.join(self.temp_dir, filename)
        
        # Tạo ảnh màu đơn giản
        image_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        image = Image.fromarray(image_array)
        image.save(image_path)
        
        return image_path
    
    def create_test_audio(self, filename, duration=5):
        """Tạo file audio test gi<PERSON> lập"""
        audio_path = os.path.join(self.temp_dir, filename)
        
        # Tạo file rỗng để test (trong thực tế cần file audio thật)
        with open(audio_path, 'wb') as f:
            f.write(b'fake audio data')
        
        return audio_path
    
    def test_supported_formats(self):
        """Test các định dạng được hỗ trợ"""
        expected_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        expected_audio_formats = ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg']
        
        self.assertEqual(self.renderer.supported_image_formats, expected_image_formats)
        self.assertEqual(self.renderer.supported_audio_formats, expected_audio_formats)
    
    def test_validate_files_success(self):
        """Test validation thành công"""
        image_path = self.create_test_image("test.jpg")
        audio_path = self.create_test_audio("test.mp3")
        
        # Không nên raise exception
        try:
            self.renderer.validate_files(image_path, audio_path)
        except Exception as e:
            self.fail(f"validate_files raised {e} unexpectedly!")
    
    def test_validate_files_image_not_found(self):
        """Test validation khi file ảnh không tồn tại"""
        audio_path = self.create_test_audio("test.mp3")
        
        with self.assertRaises(FileNotFoundError):
            self.renderer.validate_files("nonexistent.jpg", audio_path)
    
    def test_validate_files_audio_not_found(self):
        """Test validation khi file audio không tồn tại"""
        image_path = self.create_test_image("test.jpg")
        
        with self.assertRaises(FileNotFoundError):
            self.renderer.validate_files(image_path, "nonexistent.mp3")
    
    def test_validate_files_unsupported_image_format(self):
        """Test validation với định dạng ảnh không hỗ trợ"""
        # Tạo file với extension không hỗ trợ
        unsupported_path = os.path.join(self.temp_dir, "test.xyz")
        with open(unsupported_path, 'w') as f:
            f.write("fake")
        
        audio_path = self.create_test_audio("test.mp3")
        
        with self.assertRaises(ValueError):
            self.renderer.validate_files(unsupported_path, audio_path)
    
    def test_validate_files_unsupported_audio_format(self):
        """Test validation với định dạng audio không hỗ trợ"""
        image_path = self.create_test_image("test.jpg")
        
        # Tạo file với extension không hỗ trợ
        unsupported_path = os.path.join(self.temp_dir, "test.xyz")
        with open(unsupported_path, 'w') as f:
            f.write("fake")
        
        with self.assertRaises(ValueError):
            self.renderer.validate_files(image_path, unsupported_path)
    
    def test_resize_image_to_video_size(self):
        """Test resize ảnh"""
        image_path = self.create_test_image("test.jpg", 1600, 900)
        
        resized_path = self.renderer.resize_image_to_video_size(
            image_path, target_width=1920, target_height=1080
        )
        
        # Kiểm tra file được tạo
        self.assertTrue(os.path.exists(resized_path))
        
        # Kiểm tra kích thước
        with Image.open(resized_path) as img:
            self.assertEqual(img.size, (1920, 1080))
        
        # Cleanup
        if os.path.exists(resized_path):
            os.remove(resized_path)
    
    @patch('video_renderer.AudioFileClip')
    def test_get_audio_duration(self, mock_audio_clip):
        """Test lấy thời lượng audio"""
        # Mock audio clip
        mock_clip = MagicMock()
        mock_clip.duration = 180.5  # 3 phút 30 giây
        mock_audio_clip.return_value = mock_clip
        
        audio_path = self.create_test_audio("test.mp3")
        duration = self.renderer.get_audio_duration(audio_path)
        
        self.assertEqual(duration, 180.5)
        mock_clip.close.assert_called_once()
    
    @patch('video_renderer.AudioFileClip')
    @patch('video_renderer.ImageClip')
    def test_render_video_mock(self, mock_image_clip, mock_audio_clip):
        """Test render video với mock"""
        # Setup mocks
        mock_audio = MagicMock()
        mock_audio.duration = 10.0
        mock_audio_clip.return_value = mock_audio
        
        mock_image = MagicMock()
        mock_image_clip.return_value = mock_image
        
        mock_final_video = MagicMock()
        mock_image.with_audio.return_value = mock_final_video
        
        # Create test files
        image_path = self.create_test_image("test.jpg")
        audio_path = self.create_test_audio("test.mp3")
        output_path = os.path.join(self.temp_dir, "output.mp4")
        
        # Test render
        self.renderer.render_video(image_path, audio_path, output_path)
        
        # Verify calls
        mock_audio_clip.assert_called()
        mock_image_clip.assert_called()
        mock_image.with_audio.assert_called_with(mock_audio)
        mock_final_video.write_videofile.assert_called()


class TestVideoRendererIntegration(unittest.TestCase):
    """Integration tests - cần file thật để test"""
    
    def setUp(self):
        self.renderer = VideoRenderer()
    
    def test_integration_with_real_files(self):
        """Test integration với file thật (skip nếu không có file)"""
        # Tạo ảnh test đơn giản
        test_image = "test_image.jpg"
        if not os.path.exists(test_image):
            # Tạo ảnh test
            img = Image.new('RGB', (800, 600), color='red')
            img.save(test_image)
        
        # Skip test nếu không có file audio
        test_audio = "test_audio.mp3"
        if not os.path.exists(test_audio):
            self.skipTest("Không có file audio test")
        
        output_video = "test_output.mp4"
        
        try:
            self.renderer.render_video(test_image, test_audio, output_video)
            self.assertTrue(os.path.exists(output_video))
        except Exception as e:
            self.skipTest(f"Integration test failed: {e}")
        finally:
            # Cleanup
            for file in [test_image, output_video]:
                if os.path.exists(file):
                    os.remove(file)


if __name__ == '__main__':
    # Chạy tests
    unittest.main(verbosity=2)
