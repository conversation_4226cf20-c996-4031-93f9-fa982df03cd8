#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI cho Batch Video Renderer
Giao diện quản lý và theo dõi batch processing
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import json
from datetime import datetime
from batch_renderer import BatchRenderer, RenderJob


class BatchRendererGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Batch Video Renderer")
        self.root.geometry("900x700")
        
        self.batch_renderer = BatchRenderer()
        self.batch_renderer.set_progress_callback(self.on_progress_update)
        
        self.setup_ui()
        self.update_status()
    
    def setup_ui(self):
        # Main notebook
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Import Jobs
        self.setup_import_tab(notebook)
        
        # Tab 2: Job Queue
        self.setup_queue_tab(notebook)
        
        # Tab 3: Processing
        self.setup_processing_tab(notebook)
        
        # Tab 4: Results
        self.setup_results_tab(notebook)
    
    def setup_import_tab(self, notebook):
        """Tab import jobs"""
        import_frame = ttk.Frame(notebook)
        notebook.add(import_frame, text="Import Jobs")
        
        # Title
        ttk.Label(import_frame, text="Import Jobs", 
                 font=("Arial", 14, "bold")).pack(pady=10)
        
        # Import methods frame
        methods_frame = ttk.LabelFrame(import_frame, text="Import Methods", padding="10")
        methods_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # CSV Import
        csv_frame = ttk.Frame(methods_frame)
        csv_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(csv_frame, text="CSV File:").pack(side=tk.LEFT)
        self.csv_path_var = tk.StringVar()
        ttk.Entry(csv_frame, textvariable=self.csv_path_var, width=50).pack(
            side=tk.LEFT, padx=(10, 5))
        ttk.Button(csv_frame, text="Browse", 
                  command=self.browse_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(csv_frame, text="Import CSV", 
                  command=self.import_csv).pack(side=tk.LEFT, padx=5)
        
        # JSON Import
        json_frame = ttk.Frame(methods_frame)
        json_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(json_frame, text="JSON File:").pack(side=tk.LEFT)
        self.json_path_var = tk.StringVar()
        ttk.Entry(json_frame, textvariable=self.json_path_var, width=50).pack(
            side=tk.LEFT, padx=(10, 5))
        ttk.Button(json_frame, text="Browse", 
                  command=self.browse_json).pack(side=tk.LEFT, padx=5)
        ttk.Button(json_frame, text="Import JSON", 
                  command=self.import_json).pack(side=tk.LEFT, padx=5)
        
        # Folder Import
        folder_frame = ttk.Frame(methods_frame)
        folder_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(folder_frame, text="Folder:").pack(side=tk.LEFT)
        self.folder_path_var = tk.StringVar()
        ttk.Entry(folder_frame, textvariable=self.folder_path_var, width=50).pack(
            side=tk.LEFT, padx=(10, 5))
        ttk.Button(folder_frame, text="Browse", 
                  command=self.browse_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(folder_frame, text="Auto-Pair", 
                  command=self.import_folder).pack(side=tk.LEFT, padx=5)
        
        # Manual Add
        manual_frame = ttk.LabelFrame(import_frame, text="Manual Add Job", padding="10")
        manual_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Job ID
        id_frame = ttk.Frame(manual_frame)
        id_frame.pack(fill=tk.X, pady=2)
        ttk.Label(id_frame, text="Job ID:", width=15).pack(side=tk.LEFT)
        self.job_id_var = tk.StringVar()
        ttk.Entry(id_frame, textvariable=self.job_id_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Image
        img_frame = ttk.Frame(manual_frame)
        img_frame.pack(fill=tk.X, pady=2)
        ttk.Label(img_frame, text="Image:", width=15).pack(side=tk.LEFT)
        self.manual_image_var = tk.StringVar()
        ttk.Entry(img_frame, textvariable=self.manual_image_var).pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(img_frame, text="Browse", 
                  command=self.browse_manual_image).pack(side=tk.LEFT)
        
        # Audio
        audio_frame = ttk.Frame(manual_frame)
        audio_frame.pack(fill=tk.X, pady=2)
        ttk.Label(audio_frame, text="Audio:", width=15).pack(side=tk.LEFT)
        self.manual_audio_var = tk.StringVar()
        ttk.Entry(audio_frame, textvariable=self.manual_audio_var).pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(audio_frame, text="Browse", 
                  command=self.browse_manual_audio).pack(side=tk.LEFT)
        
        # Output
        output_frame = ttk.Frame(manual_frame)
        output_frame.pack(fill=tk.X, pady=2)
        ttk.Label(output_frame, text="Output:", width=15).pack(side=tk.LEFT)
        self.manual_output_var = tk.StringVar()
        ttk.Entry(output_frame, textvariable=self.manual_output_var).pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="Browse", 
                  command=self.browse_manual_output).pack(side=tk.LEFT)
        
        # Add button
        ttk.Button(manual_frame, text="Add Job", 
                  command=self.add_manual_job).pack(pady=10)
        
        # Template generators
        template_frame = ttk.LabelFrame(import_frame, text="Generate Templates", padding="10")
        template_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(template_frame, text="Generate CSV Template", 
                  command=self.generate_csv_template).pack(side=tk.LEFT, padx=5)
        ttk.Button(template_frame, text="Generate JSON Template", 
                  command=self.generate_json_template).pack(side=tk.LEFT, padx=5)
    
    def setup_queue_tab(self, notebook):
        """Tab hiển thị job queue"""
        queue_frame = ttk.Frame(notebook)
        notebook.add(queue_frame, text="Job Queue")
        
        # Title và controls
        header_frame = ttk.Frame(queue_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(header_frame, text="Job Queue", 
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        
        ttk.Button(header_frame, text="Clear All", 
                  command=self.clear_queue).pack(side=tk.RIGHT, padx=5)
        ttk.Button(header_frame, text="Refresh", 
                  command=self.refresh_queue).pack(side=tk.RIGHT, padx=5)
        
        # Job list
        list_frame = ttk.Frame(queue_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Treeview
        columns = ("ID", "Image", "Audio", "Output", "Status")
        self.job_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.job_tree.heading(col, text=col)
            self.job_tree.column(col, width=150)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.job_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.job_tree.xview)
        self.job_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.job_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_processing_tab(self, notebook):
        """Tab processing control"""
        process_frame = ttk.Frame(notebook)
        notebook.add(process_frame, text="Processing")
        
        # Title
        ttk.Label(process_frame, text="Batch Processing Control", 
                 font=("Arial", 14, "bold")).pack(pady=10)
        
        # Status frame
        status_frame = ttk.LabelFrame(process_frame, text="Status", padding="10")
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_text = tk.Text(status_frame, height=8, wrap=tk.WORD)
        status_scroll = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scroll.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(process_frame, text="Progress", padding="10")
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.progress_var = tk.StringVar(value="Ready")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack()
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # Current job frame
        current_frame = ttk.LabelFrame(process_frame, text="Current Job", padding="10")
        current_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.current_job_var = tk.StringVar(value="None")
        ttk.Label(current_frame, textvariable=self.current_job_var).pack()
        
        # Control buttons
        control_frame = ttk.Frame(process_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = ttk.Button(control_frame, text="Start Processing", 
                                      command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Stop Processing", 
                                     command=self.stop_processing, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="Clear Log", 
                  command=self.clear_log).pack(side=tk.RIGHT, padx=5)
    
    def setup_results_tab(self, notebook):
        """Tab kết quả"""
        results_frame = ttk.Frame(notebook)
        notebook.add(results_frame, text="Results")
        
        # Title
        ttk.Label(results_frame, text="Processing Results", 
                 font=("Arial", 14, "bold")).pack(pady=10)
        
        # Summary frame
        summary_frame = ttk.LabelFrame(results_frame, text="Summary", padding="10")
        summary_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.summary_text = tk.Text(summary_frame, height=6, wrap=tk.WORD)
        summary_scroll = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=self.summary_text.yview)
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Export frame
        export_frame = ttk.LabelFrame(results_frame, text="Export Results", padding="10")
        export_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(export_frame, text="Export to JSON", 
                  command=self.export_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(export_frame, text="Open Output Folder", 
                  command=self.open_output_folder).pack(side=tk.LEFT, padx=5)
    
    # Event handlers
    def browse_csv(self):
        filename = filedialog.askopenfilename(
            title="Select CSV file",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.csv_path_var.set(filename)
    
    def browse_json(self):
        filename = filedialog.askopenfilename(
            title="Select JSON file",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.json_path_var.set(filename)
    
    def browse_folder(self):
        folder = filedialog.askdirectory(title="Select folder with images and audio")
        if folder:
            self.folder_path_var.set(folder)
    
    def browse_manual_image(self):
        filename = filedialog.askopenfilename(
            title="Select image file",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"), ("All files", "*.*")]
        )
        if filename:
            self.manual_image_var.set(filename)
    
    def browse_manual_audio(self):
        filename = filedialog.askopenfilename(
            title="Select audio file",
            filetypes=[("Audio files", "*.mp3 *.wav *.aac *.m4a *.flac *.ogg"), ("All files", "*.*")]
        )
        if filename:
            self.manual_audio_var.set(filename)
    
    def browse_manual_output(self):
        filename = filedialog.asksaveasfilename(
            title="Select output video file",
            defaultextension=".mp4",
            filetypes=[("MP4 files", "*.mp4"), ("All files", "*.*")]
        )
        if filename:
            self.manual_output_var.set(filename)
    
    def import_csv(self):
        try:
            if not self.csv_path_var.get():
                messagebox.showerror("Error", "Please select a CSV file")
                return
            
            self.batch_renderer.add_jobs_from_csv(self.csv_path_var.get())
            self.refresh_queue()
            messagebox.showinfo("Success", "Jobs imported from CSV successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import CSV: {e}")
    
    def import_json(self):
        try:
            if not self.json_path_var.get():
                messagebox.showerror("Error", "Please select a JSON file")
                return
            
            self.batch_renderer.add_jobs_from_json(self.json_path_var.get())
            self.refresh_queue()
            messagebox.showinfo("Success", "Jobs imported from JSON successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import JSON: {e}")
    
    def import_folder(self):
        try:
            if not self.folder_path_var.get():
                messagebox.showerror("Error", "Please select a folder")
                return
            
            jobs = self.batch_renderer.add_jobs_from_folder(self.folder_path_var.get())
            self.refresh_queue()
            messagebox.showinfo("Success", f"Auto-paired {len(jobs)} jobs from folder")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to import from folder: {e}")
    
    def add_manual_job(self):
        try:
            if not all([self.manual_image_var.get(), self.manual_audio_var.get(), self.manual_output_var.get()]):
                messagebox.showerror("Error", "Please fill all fields")
                return
            
            job_id = self.job_id_var.get() or f"manual_{datetime.now().strftime('%H%M%S')}"
            
            job = RenderJob(
                id=job_id,
                image_path=self.manual_image_var.get(),
                audio_path=self.manual_audio_var.get(),
                output_path=self.manual_output_var.get()
            )
            
            self.batch_renderer.add_job(job)
            self.refresh_queue()
            
            # Clear fields
            self.job_id_var.set("")
            self.manual_image_var.set("")
            self.manual_audio_var.set("")
            self.manual_output_var.set("")
            
            messagebox.showinfo("Success", f"Job '{job_id}' added successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add job: {e}")
    
    def generate_csv_template(self):
        filename = filedialog.asksaveasfilename(
            title="Save CSV template",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")]
        )
        if filename:
            try:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['id', 'image_path', 'audio_path', 'output_path', 'width', 'height'])
                    writer.writerow(['job_1', 'image1.jpg', 'audio1.mp3', 'output1.mp4', '1920', '1080'])
                    writer.writerow(['job_2', 'image2.jpg', 'audio2.mp3', 'output2.mp4', '1280', '720'])
                
                messagebox.showinfo("Success", f"CSV template saved: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save template: {e}")
    
    def generate_json_template(self):
        filename = filedialog.asksaveasfilename(
            title="Save JSON template",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json")]
        )
        if filename:
            try:
                template = {
                    "jobs": [
                        {
                            "id": "job_1",
                            "image_path": "image1.jpg",
                            "audio_path": "audio1.mp3",
                            "output_path": "output1.mp4",
                            "width": 1920,
                            "height": 1080
                        },
                        {
                            "id": "job_2",
                            "image_path": "image2.jpg",
                            "audio_path": "audio2.mp3",
                            "output_path": "output2.mp4",
                            "width": 1280,
                            "height": 720
                        }
                    ]
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(template, f, indent=2)
                
                messagebox.showinfo("Success", f"JSON template saved: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save template: {e}")
    
    def refresh_queue(self):
        # Clear existing items
        for item in self.job_tree.get_children():
            self.job_tree.delete(item)
        
        # Add pending jobs
        temp_queue = []
        while not self.batch_renderer.job_queue.empty():
            job = self.batch_renderer.job_queue.get()
            temp_queue.append(job)
            self.job_tree.insert("", tk.END, values=(
                job.id, 
                os.path.basename(job.image_path),
                os.path.basename(job.audio_path),
                os.path.basename(job.output_path),
                job.status
            ))
        
        # Put jobs back
        for job in temp_queue:
            self.batch_renderer.job_queue.put(job)
        
        # Add completed jobs
        for job in self.batch_renderer.completed_jobs:
            self.job_tree.insert("", tk.END, values=(
                job.id,
                os.path.basename(job.image_path),
                os.path.basename(job.audio_path),
                os.path.basename(job.output_path),
                job.status
            ))
        
        # Add failed jobs
        for job in self.batch_renderer.failed_jobs:
            self.job_tree.insert("", tk.END, values=(
                job.id,
                os.path.basename(job.image_path),
                os.path.basename(job.audio_path),
                os.path.basename(job.output_path),
                job.status
            ))
    
    def clear_queue(self):
        if messagebox.askyesno("Confirm", "Clear all jobs from queue?"):
            # Clear queue
            while not self.batch_renderer.job_queue.empty():
                self.batch_renderer.job_queue.get()
            
            # Clear completed/failed
            self.batch_renderer.completed_jobs.clear()
            self.batch_renderer.failed_jobs.clear()
            
            self.refresh_queue()
            self.update_status()
    
    def start_processing(self):
        if self.batch_renderer.job_queue.empty():
            messagebox.showwarning("Warning", "No jobs in queue")
            return
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        # Start processing in background
        self.batch_renderer.start_processing_async()
    
    def stop_processing(self):
        self.batch_renderer.stop_processing()
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
    
    def clear_log(self):
        self.status_text.delete(1.0, tk.END)
    
    def export_results(self):
        filename = filedialog.asksaveasfilename(
            title="Export results",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json")]
        )
        if filename:
            try:
                self.batch_renderer.export_results(filename)
                messagebox.showinfo("Success", f"Results exported: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export: {e}")
    
    def open_output_folder(self):
        # Open default output folder
        output_folder = "batch_output"
        if os.path.exists(output_folder):
            os.startfile(output_folder)
        else:
            messagebox.showinfo("Info", f"Output folder '{output_folder}' does not exist yet")
    
    def on_progress_update(self, job, status):
        """Callback khi có update từ batch renderer"""
        self.root.after(0, lambda: self.update_progress(job, status))
    
    def update_progress(self, job, status):
        """Update UI progress"""
        # Update status text
        timestamp = datetime.now().strftime("%H:%M:%S")
        if job.status == "completed":
            message = f"[{timestamp}] Completed: {job.id} ({job.duration:.2f}s)\n"
        elif job.status == "failed":
            message = f"[{timestamp}] Failed: {job.id} - {job.error_message}\n"
        else:
            message = f"[{timestamp}] Processing: {job.id}\n"
        
        self.status_text.insert(tk.END, message)
        self.status_text.see(tk.END)
        
        # Update progress
        total = status['pending'] + status['completed'] + status['failed']
        if total > 0:
            completed = status['completed'] + status['failed']
            progress = (completed / total) * 100
            self.progress_bar['value'] = progress
            self.progress_var.set(f"Progress: {completed}/{total} ({progress:.1f}%)")
        
        # Update current job
        if status['current']:
            self.current_job_var.set(f"Current: {status['current']}")
        else:
            self.current_job_var.set("None")
        
        # Update buttons
        if not status['is_running']:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
        
        # Refresh queue and summary
        self.refresh_queue()
        self.update_summary()
    
    def update_status(self):
        """Update status periodically"""
        status = self.batch_renderer.get_queue_status()
        
        # Update summary
        self.update_summary()
        
        # Schedule next update
        self.root.after(1000, self.update_status)
    
    def update_summary(self):
        """Update summary text"""
        status = self.batch_renderer.get_queue_status()
        
        summary = f"""Queue Status:
- Pending: {status['pending']}
- Completed: {status['completed']}
- Failed: {status['failed']}
- Currently Processing: {status['current'] or 'None'}
- Is Running: {status['is_running']}

Total Jobs: {status['pending'] + status['completed'] + status['failed']}
"""
        
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, summary)


def main():
    root = tk.Tk()
    app = BatchRendererGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
