#!/usr/bin/env python3
"""
Tạo file audio dài hơn để test khả năng render video dài
"""

import wave
import struct
import math
import random


def create_long_audio(filename="long_audio.wav", duration_minutes=3):
    """Tạo file audio dài với nhiều variation"""
    
    duration_seconds = duration_minutes * 60
    sample_rate = 44100
    
    print(f"🎵 Đang tạo file audio dài {duration_minutes} phút...")
    
    frames = []
    
    # Tạo âm nhạc với variation
    for i in range(int(sample_rate * duration_seconds)):
        t = i / sample_rate
        
        # Fade in/out
        fade_duration = 5.0  # 5 giây fade
        if t < fade_duration:
            amplitude = t / fade_duration
        elif t > duration_seconds - fade_duration:
            amplitude = (duration_seconds - t) / fade_duration
        else:
            amplitude = 1.0
        
        # Tạo âm nhạc phức tạp hơn
        value = 0
        
        # Base frequency thay đổi theo thời gian
        base_freq = 220 + 50 * math.sin(2 * math.pi * t / 30)  # Thay đổi mỗi 30 giây
        
        # Harmony
        value += 0.3 * math.sin(2 * math.pi * base_freq * t)  # Root
        value += 0.2 * math.sin(2 * math.pi * base_freq * 1.25 * t)  # Third
        value += 0.15 * math.sin(2 * math.pi * base_freq * 1.5 * t)  # Fifth
        value += 0.1 * math.sin(2 * math.pi * base_freq * 2 * t)  # Octave
        
        # Thêm rhythm
        beat_freq = 2  # 2 beat per second
        beat_amplitude = 0.3 + 0.2 * math.sin(2 * math.pi * beat_freq * t)
        value *= beat_amplitude
        
        # Thêm một chút noise cho tự nhiên
        if i % 1000 == 0:  # Mỗi 1000 sample
            value += 0.05 * (random.random() - 0.5)
        
        value *= amplitude * 0.4  # Giảm âm lượng tổng thể
        
        # Chuyển đổi sang 16-bit
        frames.append(struct.pack('<h', int(value * 32767)))
        
        # Progress indicator
        if i % (sample_rate * 10) == 0:  # Mỗi 10 giây
            progress = (i / (sample_rate * duration_seconds)) * 100
            print(f"  Progress: {progress:.1f}%")
    
    # Lưu file WAV
    with wave.open(filename, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(b''.join(frames))
    
    file_size_mb = len(frames) * 2 / (1024 * 1024)  # 2 bytes per frame
    print(f"✅ Đã tạo {filename}")
    print(f"   Thời lượng: {duration_minutes} phút")
    print(f"   Kích thước: {file_size_mb:.1f} MB")


def main():
    print("🎼 Tạo file audio dài để test...")
    
    # Tạo file 3 phút (có thể thay đổi)
    create_long_audio("long_audio_3min.wav", duration_minutes=3)
    
    print("\n🚀 Bây giờ bạn có thể test với:")
    print("  python video_renderer.py test_image.jpg long_audio_3min.wav -o long_video.mp4")
    print("\n⚠️  Lưu ý: Video 3 phút có thể mất 5-15 phút để render tùy thuộc cấu hình máy")


if __name__ == "__main__":
    main()
