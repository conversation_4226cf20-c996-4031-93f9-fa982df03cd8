# 🎬 Batch Video Renderer - Hướng dẫn sử dụng

## 📋 Tổng quan

Batch Video Renderer cho phép bạn render **hàng loạt video** (100+ video) một cách tự động và tuần tự. Tool hỗ trợ nhiều cách import jobs và theo dõi tiến trình real-time.

## 🚀 Tính năng chính

✅ **Batch Processing**: Render 100+ video tuần tự  
✅ **Multiple Import**: CSV, JSON, Auto-pair từ folder  
✅ **Progress Tracking**: <PERSON> dõi tiến trình real-time  
✅ **Error Handling**: <PERSON>ử lý lỗi và tiếp tục với job tiếp theo  
✅ **Logging**: Ghi log chi tiết  
✅ **Results Export**: Export kết quả ra JSON  
✅ **GUI & CLI**: Cả giao diện đồ họa và dòng lệnh  

## 📁 Cấu trúc Files

```
📁 tool render video/
├── 📄 batch_renderer.py      # Core batch processing engine
├── 📄 batch_gui.py          # GUI cho batch processing  
├── 📄 create_batch_demo.py  # Tạo demo data
├── 📁 demo_batch/           # Demo data
│   ├── 📄 batch_jobs.csv    # CSV import file
│   ├── 📄 batch_jobs.json   # JSON import file
│   ├── 📁 mixed_files/      # Auto-pair folder
│   └── 📁 output/           # Video outputs
└── 📄 BATCH_PROCESSING_GUIDE.md  # File này
```

## 🔧 Cách sử dụng

### 1. CLI (Command Line Interface)

#### Import từ CSV:
```bash
python batch_renderer.py --csv jobs.csv --log batch.log
```

#### Import từ JSON:
```bash
python batch_renderer.py --json jobs.json --output-folder my_output
```

#### Auto-pair từ folder:
```bash
python batch_renderer.py --folder my_folder --output-folder output
```

### 2. GUI (Graphical User Interface)

```bash
python batch_gui.py
```

GUI có 4 tabs:
- **Import Jobs**: Import từ CSV/JSON/Folder hoặc thêm manual
- **Job Queue**: Xem danh sách jobs
- **Processing**: Điều khiển và theo dõi tiến trình
- **Results**: Xem kết quả và export

## 📄 Định dạng Import Files

### CSV Format:
```csv
id,image_path,audio_path,output_path,width,height
job_1,image1.jpg,audio1.mp3,output1.mp4,1920,1080
job_2,image2.jpg,audio2.mp3,output2.mp4,1280,720
```

### JSON Format:
```json
{
  "jobs": [
    {
      "id": "job_1",
      "image_path": "image1.jpg",
      "audio_path": "audio1.mp3", 
      "output_path": "output1.mp4",
      "width": 1920,
      "height": 1080
    }
  ]
}
```

### Auto-pair Folder:
```
📁 my_folder/
├── 🖼️ content_01.jpg
├── 🎵 content_01.mp3
├── 🖼️ content_02.jpg
├── 🎵 content_02.mp3
└── ...
```
Tool sẽ tự động ghép file có cùng tên.

## 📊 Performance

### Test Results (5 videos, 15 giây mỗi video):
- **Thời gian**: 2.56 giây total (~0.5s/video)
- **Success rate**: 100% (5/5 completed)
- **Memory usage**: ~300-500MB
- **CPU usage**: Moderate

### Ước tính cho 100 videos:
- **Thời gian**: ~50 giây (video ngắn)
- **Thời gian**: ~5-10 phút (video 3 phút)
- **Disk space**: ~100-500MB output

## 🔄 Workflow

```mermaid
graph TD
    A[Chuẩn bị Files] --> B[Import Jobs]
    B --> C[Kiểm tra Queue]
    C --> D[Start Processing]
    D --> E[Render Video 1]
    E --> F[Render Video 2]
    F --> G[...]
    G --> H[Render Video N]
    H --> I[Export Results]
    I --> J[Hoàn thành]
```

## 📝 Logging

Tool ghi log chi tiết:
```
[2025-08-20 01:32:13] Starting batch processing: 5 jobs
[2025-08-20 01:32:13] Starting job: batch_job_01
[2025-08-20 01:32:13] Completed job: batch_job_01 in 0.52s
[2025-08-20 01:32:15] Batch processing completed in 2.56s
```

## 📤 Export Results

Kết quả được export ra JSON:
```json
{
  "summary": {
    "total_jobs": 5,
    "completed": 5,
    "failed": 0
  },
  "completed_jobs": [...],
  "failed_jobs": [...]
}
```

## 🛠️ Advanced Usage

### 1. Tạo Template Files:
```bash
# Tạo demo data
python create_batch_demo.py

# Hoặc dùng GUI để generate templates
```

### 2. Custom Output Structure:
```bash
python batch_renderer.py --csv jobs.csv --output-folder "output/$(date)"
```

### 3. Error Recovery:
- Tool tự động skip job lỗi và tiếp tục
- Log chi tiết lỗi để debug
- Export danh sách failed jobs

## 🎯 Use Cases

### 1. **Podcast Series** (100 episodes):
```
📁 podcast_season1/
├── 🖼️ episode_001_cover.jpg + 🎵 episode_001_audio.mp3
├── 🖼️ episode_002_cover.jpg + 🎵 episode_002_audio.mp3
└── ... (98 episodes more)
```

### 2. **Music Album** (20 tracks):
```csv
id,image_path,audio_path,output_path
track_01,album_cover.jpg,track_01.mp3,videos/track_01.mp4
track_02,album_cover.jpg,track_02.mp3,videos/track_02.mp4
```

### 3. **Course Content** (50 lessons):
```json
{
  "jobs": [
    {
      "id": "lesson_01",
      "image_path": "course_thumbnail.jpg",
      "audio_path": "lesson_01_audio.mp3",
      "output_path": "course_videos/lesson_01.mp4"
    }
  ]
}
```

## ⚡ Tips & Best Practices

### 1. **Chuẩn bị Files**:
- Đặt tên file có quy tắc: `content_01.jpg`, `content_01.mp3`
- Kiểm tra định dạng file trước khi import
- Backup files quan trọng

### 2. **Optimize Performance**:
- Sử dụng SSD cho tốc độ I/O
- Đóng các ứng dụng khác khi render
- Monitor disk space

### 3. **Error Prevention**:
- Test với 2-3 jobs trước khi chạy full batch
- Kiểm tra đường dẫn files
- Đảm bảo đủ disk space

### 4. **Monitoring**:
- Sử dụng log file để theo dõi
- Check progress qua GUI
- Export results để backup

## 🚨 Troubleshooting

### Lỗi thường gặp:

**1. File not found:**
```
Error: File không tồn tại: image.jpg
```
→ Kiểm tra đường dẫn file trong CSV/JSON

**2. Insufficient disk space:**
```
Error: No space left on device
```
→ Dọn dẹp disk hoặc chọn output folder khác

**3. Memory error:**
```
Error: MemoryError
```
→ Giảm số jobs đồng thời hoặc restart tool

**4. Codec error:**
```
Error: codec not found
```
→ Cài đặt/cập nhật FFmpeg

## 📞 Support

- **Documentation**: README.md
- **Demo**: `python create_batch_demo.py`
- **Test**: `python test_video_renderer.py`
- **Issues**: Kiểm tra log files

## 🎉 Kết luận

Batch Video Renderer là giải pháp hoàn hảo cho việc render hàng loạt video:

✅ **Hiệu quả**: ~0.5s/video cho content ngắn  
✅ **Ổn định**: Error handling tốt  
✅ **Linh hoạt**: Nhiều cách import  
✅ **Thân thiện**: GUI và CLI  
✅ **Scalable**: Hỗ trợ 100+ videos  

**Ready for production!** 🚀
