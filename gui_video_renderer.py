#!/usr/bin/env python3
"""
GUI version của tool render video
Giao diện đồ họa đơn giản để render ảnh và nhạc thành video
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from video_renderer import VideoRenderer


class VideoRendererGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Video Renderer Tool")
        self.root.geometry("600x500")
        
        self.renderer = VideoRenderer()
        self.is_rendering = False
        
        self.setup_ui()
    
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Video Renderer Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Image file selection
        ttk.Label(main_frame, text="File ảnh:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.image_path_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.image_path_var, width=50).grid(
            row=1, column=1, padx=(10, 5), pady=5)
        ttk.Button(main_frame, text="Chọn", 
                  command=self.select_image_file).grid(row=1, column=2, pady=5)
        
        # Audio file selection
        ttk.Label(main_frame, text="File audio:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.audio_path_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.audio_path_var, width=50).grid(
            row=2, column=1, padx=(10, 5), pady=5)
        ttk.Button(main_frame, text="Chọn", 
                  command=self.select_audio_file).grid(row=2, column=2, pady=5)
        
        # Output file selection
        ttk.Label(main_frame, text="File đầu ra:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.output_path_var = tk.StringVar(value="output_video.mp4")
        ttk.Entry(main_frame, textvariable=self.output_path_var, width=50).grid(
            row=3, column=1, padx=(10, 5), pady=5)
        ttk.Button(main_frame, text="Chọn", 
                  command=self.select_output_file).grid(row=3, column=2, pady=5)
        
        # Video settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Cài đặt video", padding="10")
        settings_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        # Width setting
        ttk.Label(settings_frame, text="Chiều rộng:").grid(row=0, column=0, sticky=tk.W)
        self.width_var = tk.StringVar(value="1920")
        width_combo = ttk.Combobox(settings_frame, textvariable=self.width_var, 
                                  values=["1920", "1280", "854", "640"], width=10)
        width_combo.grid(row=0, column=1, padx=(10, 20))
        
        # Height setting
        ttk.Label(settings_frame, text="Chiều cao:").grid(row=0, column=2, sticky=tk.W)
        self.height_var = tk.StringVar(value="1080")
        height_combo = ttk.Combobox(settings_frame, textvariable=self.height_var,
                                   values=["1080", "720", "480", "360"], width=10)
        height_combo.grid(row=0, column=3, padx=(10, 0))
        
        # Progress frame
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Sẵn sàng")
        ttk.Label(progress_frame, textvariable=self.progress_var).grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=6, column=0, columnspan=3, pady=20)
        
        # Render button
        self.render_button = ttk.Button(buttons_frame, text="Bắt đầu Render", 
                                       command=self.start_render)
        self.render_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Clear button
        ttk.Button(buttons_frame, text="Xóa tất cả", 
                  command=self.clear_all).pack(side=tk.LEFT)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        progress_frame.columnconfigure(0, weight=1)
    
    def select_image_file(self):
        filename = filedialog.askopenfilename(
            title="Chọn file ảnh",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.image_path_var.set(filename)
    
    def select_audio_file(self):
        filename = filedialog.askopenfilename(
            title="Chọn file audio",
            filetypes=[
                ("Audio files", "*.mp3 *.wav *.aac *.m4a *.flac *.ogg"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.audio_path_var.set(filename)
    
    def select_output_file(self):
        filename = filedialog.asksaveasfilename(
            title="Chọn nơi lưu video",
            defaultextension=".mp4",
            filetypes=[
                ("MP4 files", "*.mp4"),
                ("AVI files", "*.avi"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.output_path_var.set(filename)
    
    def clear_all(self):
        self.image_path_var.set("")
        self.audio_path_var.set("")
        self.output_path_var.set("output_video.mp4")
        self.width_var.set("1920")
        self.height_var.set("1080")
        self.progress_var.set("Sẵn sàng")
    
    def validate_inputs(self):
        if not self.image_path_var.get():
            messagebox.showerror("Lỗi", "Vui lòng chọn file ảnh")
            return False
        
        if not self.audio_path_var.get():
            messagebox.showerror("Lỗi", "Vui lòng chọn file audio")
            return False
        
        if not self.output_path_var.get():
            messagebox.showerror("Lỗi", "Vui lòng chọn file đầu ra")
            return False
        
        try:
            width = int(self.width_var.get())
            height = int(self.height_var.get())
            if width <= 0 or height <= 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("Lỗi", "Chiều rộng và chiều cao phải là số nguyên dương")
            return False
        
        return True
    
    def start_render(self):
        if self.is_rendering:
            messagebox.showwarning("Cảnh báo", "Đang render video, vui lòng đợi...")
            return
        
        if not self.validate_inputs():
            return
        
        # Start rendering in a separate thread
        self.is_rendering = True
        self.render_button.config(state='disabled')
        self.progress_bar.start()
        self.progress_var.set("Đang render video...")
        
        render_thread = threading.Thread(target=self.render_video_thread)
        render_thread.daemon = True
        render_thread.start()
    
    def render_video_thread(self):
        try:
            self.renderer.render_video(
                image_path=self.image_path_var.get(),
                audio_path=self.audio_path_var.get(),
                output_path=self.output_path_var.get(),
                video_width=int(self.width_var.get()),
                video_height=int(self.height_var.get())
            )
            
            # Update UI on success
            self.root.after(0, self.render_success)
            
        except Exception as e:
            # Update UI on error
            self.root.after(0, lambda: self.render_error(str(e)))
    
    def render_success(self):
        self.is_rendering = False
        self.render_button.config(state='normal')
        self.progress_bar.stop()
        self.progress_var.set("Render thành công!")
        
        messagebox.showinfo("Thành công", 
                           f"Video đã được tạo thành công!\nFile: {self.output_path_var.get()}")
    
    def render_error(self, error_message):
        self.is_rendering = False
        self.render_button.config(state='normal')
        self.progress_bar.stop()
        self.progress_var.set("Lỗi khi render")
        
        messagebox.showerror("Lỗi", f"Không thể render video:\n{error_message}")


def main():
    root = tk.Tk()
    app = VideoRendererGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
