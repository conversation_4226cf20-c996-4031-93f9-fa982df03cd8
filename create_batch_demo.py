#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tạo demo data cho batch processing
"""

import os
import csv
import json
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import wave
import struct
import math
import random


def create_demo_images(count=5, output_dir="demo_batch"):
    """Tạo nhiều ảnh demo với nội dung khác nhau"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    colors = [
        (255, 100, 100),  # Red
        (100, 255, 100),  # Green  
        (100, 100, 255),  # Blue
        (255, 255, 100),  # Yellow
        (255, 100, 255),  # <PERSON>genta
        (100, 255, 255),  # <PERSON><PERSON>
        (255, 150, 100),  # Orange
        (150, 100, 255),  # Purple
    ]
    
    created_images = []
    
    for i in range(count):
        # Tạo ảnh với màu và text khác nhau
        width, height = 1920, 1080
        color = colors[i % len(colors)]
        
        image = Image.new('RGB', (width, height), color)
        draw = ImageDraw.Draw(image)
        
        # Vẽ gradient
        for y in range(height):
            ratio = y / height
            r = int(color[0] * (1 - ratio * 0.3))
            g = int(color[1] * (1 - ratio * 0.3))
            b = int(color[2] * (1 - ratio * 0.3))
            draw.line([(0, y), (width, y)], fill=(r, g, b))
        
        # Vẽ hình trang trí
        center_x, center_y = width // 2, height // 2
        
        # Vòng tròn
        radius = 200 + i * 20
        draw.ellipse([
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius
        ], outline='white', width=8)
        
        # Text
        try:
            font_large = ImageFont.truetype("arial.ttf", 80)
            font_small = ImageFont.truetype("arial.ttf", 40)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Main text
        main_text = f"VIDEO {i+1}"
        text_bbox = draw.textbbox((0, 0), main_text, font=font_large)
        text_width = text_bbox[2] - text_bbox[0]
        text_x = (width - text_width) // 2
        text_y = center_y - 50
        
        # Shadow
        draw.text((text_x + 3, text_y + 3), main_text, fill='black', font=font_large)
        draw.text((text_x, text_y), main_text, fill='white', font=font_large)
        
        # Sub text
        sub_text = f"Batch Demo Image #{i+1}"
        sub_bbox = draw.textbbox((0, 0), sub_text, font=font_small)
        sub_width = sub_bbox[2] - sub_bbox[0]
        sub_x = (width - sub_width) // 2
        sub_y = center_y + 50
        
        draw.text((sub_x + 2, sub_y + 2), sub_text, fill='black', font=font_small)
        draw.text((sub_x, sub_y), sub_text, fill='white', font=font_small)
        
        # Lưu ảnh
        filename = f"image_{i+1:02d}.jpg"
        image_path = output_path / filename
        image.save(image_path, 'JPEG', quality=95)
        created_images.append(str(image_path))
        
        print(f"Created: {filename}")
    
    return created_images


def create_demo_audios(count=5, output_dir="demo_batch", duration=15):
    """Tạo nhiều file audio demo với tần số khác nhau"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Các note nhạc (Hz)
    notes = [
        220.0,  # A3
        246.9,  # B3
        261.6,  # C4
        293.7,  # D4
        329.6,  # E4
        349.2,  # F4
        392.0,  # G4
        440.0,  # A4
    ]
    
    created_audios = []
    sample_rate = 44100
    
    for i in range(count):
        base_freq = notes[i % len(notes)]
        
        frames = []
        
        for j in range(int(sample_rate * duration)):
            t = j / sample_rate
            
            # Fade in/out
            fade_duration = 1.0
            if t < fade_duration:
                amplitude = t / fade_duration
            elif t > duration - fade_duration:
                amplitude = (duration - t) / fade_duration
            else:
                amplitude = 1.0
            
            # Tạo chord với base frequency
            value = 0
            value += 0.4 * math.sin(2 * math.pi * base_freq * t)  # Root
            value += 0.3 * math.sin(2 * math.pi * base_freq * 1.25 * t)  # Third
            value += 0.2 * math.sin(2 * math.pi * base_freq * 1.5 * t)  # Fifth
            
            # Thêm rhythm
            beat_freq = 1 + i * 0.2  # Khác nhau cho mỗi file
            beat_amplitude = 0.7 + 0.3 * math.sin(2 * math.pi * beat_freq * t)
            value *= beat_amplitude
            
            value *= amplitude * 0.3
            
            frames.append(struct.pack('<h', int(value * 32767)))
        
        # Lưu file WAV
        filename = f"audio_{i+1:02d}.wav"
        audio_path = output_path / filename
        
        with wave.open(str(audio_path), 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(b''.join(frames))
        
        created_audios.append(str(audio_path))
        print(f"Created: {filename}")
    
    return created_audios


def create_csv_batch_file(images, audios, output_dir="demo_batch"):
    """Tạo file CSV cho batch processing"""
    output_path = Path(output_dir)
    csv_file = output_path / "batch_jobs.csv"
    
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['id', 'image_path', 'audio_path', 'output_path', 'width', 'height'])
        
        for i, (image, audio) in enumerate(zip(images, audios)):
            job_id = f"batch_job_{i+1:02d}"
            output_video = output_path / "output" / f"video_{i+1:02d}.mp4"
            
            writer.writerow([
                job_id,
                image,
                audio,
                str(output_video),
                1920,
                1080
            ])
    
    print(f"Created CSV: {csv_file}")
    return str(csv_file)


def create_json_batch_file(images, audios, output_dir="demo_batch"):
    """Tạo file JSON cho batch processing"""
    output_path = Path(output_dir)
    json_file = output_path / "batch_jobs.json"
    
    jobs = []
    for i, (image, audio) in enumerate(zip(images, audios)):
        job_id = f"batch_job_{i+1:02d}"
        output_video = output_path / "output" / f"video_{i+1:02d}.mp4"
        
        jobs.append({
            "id": job_id,
            "image_path": image,
            "audio_path": audio,
            "output_path": str(output_video),
            "width": 1920,
            "height": 1080
        })
    
    batch_data = {
        "description": "Demo batch processing jobs",
        "created": "2025-01-20",
        "total_jobs": len(jobs),
        "jobs": jobs
    }
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(batch_data, f, indent=2, ensure_ascii=False)
    
    print(f"Created JSON: {json_file}")
    return str(json_file)


def create_mixed_folder(images, audios, output_dir="demo_batch"):
    """Tạo folder với file ảnh và audio để test auto-pair"""
    mixed_folder = Path(output_dir) / "mixed_files"
    mixed_folder.mkdir(exist_ok=True)
    
    # Copy và rename files để có tên giống nhau
    for i, (image, audio) in enumerate(zip(images, audios)):
        base_name = f"content_{i+1:02d}"
        
        # Copy image
        image_ext = Path(image).suffix
        new_image = mixed_folder / f"{base_name}{image_ext}"
        shutil.copy2(image, new_image)
        
        # Copy audio
        audio_ext = Path(audio).suffix
        new_audio = mixed_folder / f"{base_name}{audio_ext}"
        shutil.copy2(audio, new_audio)
    
    print(f"Created mixed folder: {mixed_folder}")
    return str(mixed_folder)


def main():
    print("🎬 Creating batch processing demo data...")
    
    # Tạo thư mục output
    output_dir = "demo_batch"
    Path(output_dir).mkdir(exist_ok=True)
    Path(output_dir, "output").mkdir(exist_ok=True)
    
    # Tạo demo images và audios
    print("\n📸 Creating demo images...")
    images = create_demo_images(5, output_dir)
    
    print("\n🎵 Creating demo audios...")
    audios = create_demo_audios(5, output_dir)
    
    # Tạo batch files
    print("\n📄 Creating batch files...")
    csv_file = create_csv_batch_file(images, audios, output_dir)
    json_file = create_json_batch_file(images, audios, output_dir)
    mixed_folder = create_mixed_folder(images, audios, output_dir)
    
    print(f"\n✅ Demo data created successfully!")
    print(f"📁 Output directory: {output_dir}")
    print(f"📄 CSV file: {csv_file}")
    print(f"📄 JSON file: {json_file}")
    print(f"📁 Mixed folder: {mixed_folder}")
    
    print(f"\n🚀 Now you can test batch processing:")
    print(f"   CLI: python batch_renderer.py --csv {csv_file}")
    print(f"   CLI: python batch_renderer.py --json {json_file}")
    print(f"   CLI: python batch_renderer.py --folder {mixed_folder}")
    print(f"   GUI: python batch_gui.py")
    
    # Tạo quick test script
    test_script = Path(output_dir) / "quick_test.bat"
    with open(test_script, 'w') as f:
        f.write(f"@echo off\n")
        f.write(f"echo Testing batch processing with 5 jobs...\n")
        f.write(f"python ../batch_renderer.py --csv batch_jobs.csv --log batch_log.txt\n")
        f.write(f"echo Done! Check output folder and batch_log.txt\n")
        f.write(f"pause\n")
    
    print(f"📝 Quick test script: {test_script}")


if __name__ == "__main__":
    main()
