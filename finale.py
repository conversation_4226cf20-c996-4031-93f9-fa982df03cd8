#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎬 FINALE - Dynamic Multi-Job Video Renderer
Giao diện thân thiện cho phép thêm nhiều jobs động để render hàng loạt video
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from pathlib import Path
from datetime import datetime
from video_renderer import VideoRenderer
from batch_renderer import BatchRenderer, RenderJob

# Fix encoding for Windows console
import sys
if sys.platform == "win32":
    try:
        import codecs
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())
    except:
        pass  # Skip if encoding fix fails


class JobWidget:
    """Widget đại diện cho 1 job render video"""
    
    def __init__(self, parent_frame, job_id, on_delete_callback, on_change_callback):
        self.job_id = job_id
        self.parent_frame = parent_frame
        self.on_delete = on_delete_callback
        self.on_change = on_change_callback
        
        # Variables
        self.image_var = tk.StringVar()
        self.audio_var = tk.StringVar()
        self.output_var = tk.StringVar(value=f"output/video_{job_id:03d}.mp4")
        self.status_var = tk.StringVar(value="Ready")
        
        # Bind change events
        self.image_var.trace('w', self.on_field_change)
        self.audio_var.trace('w', self.on_field_change)
        self.output_var.trace('w', self.on_field_change)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Tạo UI cho job widget"""
        # Main frame cho job này
        self.frame = ttk.LabelFrame(self.parent_frame, text=f"📋 Job {self.job_id:03d}", padding="10")
        self.frame.pack(fill=tk.X, pady=5, padx=5)
        
        # Row 1: Image selection
        image_frame = ttk.Frame(self.frame)
        image_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(image_frame, text="Image:", width=8).pack(side=tk.LEFT)
        image_entry = ttk.Entry(image_frame, textvariable=self.image_var, width=50)
        image_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(image_frame, text="Browse", command=self.browse_image).pack(side=tk.LEFT, padx=2)
        self.image_status = ttk.Label(image_frame, text="❌", width=3)
        self.image_status.pack(side=tk.LEFT)
        
        # Row 2: Audio selection
        audio_frame = ttk.Frame(self.frame)
        audio_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(audio_frame, text="Audio:", width=8).pack(side=tk.LEFT)
        audio_entry = ttk.Entry(audio_frame, textvariable=self.audio_var, width=50)
        audio_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(audio_frame, text="Browse", command=self.browse_audio).pack(side=tk.LEFT, padx=2)
        self.audio_status = ttk.Label(audio_frame, text="❌", width=3)
        self.audio_status.pack(side=tk.LEFT)
        
        # Row 3: Output and controls
        output_frame = ttk.Frame(self.frame)
        output_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(output_frame, text="Output:", width=8).pack(side=tk.LEFT)
        output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=50)
        output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(output_frame, text="Browse", command=self.browse_output).pack(side=tk.LEFT, padx=2)
        
        # Control buttons
        ttk.Button(output_frame, text="📋", command=self.duplicate_job, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Button(output_frame, text="❌", command=self.delete_job, width=3).pack(side=tk.LEFT, padx=2)
        
        # Row 4: Status and progress
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(status_frame, text="Status:", width=8).pack(side=tk.LEFT)
        status_label = ttk.Label(status_frame, textvariable=self.status_var, width=20)
        status_label.pack(side=tk.LEFT, padx=(5, 10))
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(status_frame, mode='determinate', length=200)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # Update initial status
        self.update_status_indicators()
    
    def browse_image(self):
        """Chọn file ảnh"""
        filename = filedialog.askopenfilename(
            title=f"Select image for Job {self.job_id:03d}",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.image_var.set(filename)
            # Auto-generate output name based on image
            self.auto_generate_output_name()
    
    def browse_audio(self):
        """Chọn file audio"""
        filename = filedialog.askopenfilename(
            title=f"Select audio for Job {self.job_id:03d}",
            filetypes=[
                ("Audio files", "*.mp3 *.wav *.aac *.m4a *.flac *.ogg"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.audio_var.set(filename)
    
    def browse_output(self):
        """Chọn file output"""
        filename = filedialog.asksaveasfilename(
            title=f"Save video for Job {self.job_id:03d}",
            defaultextension=".mp4",
            filetypes=[
                ("MP4 files", "*.mp4"),
                ("AVI files", "*.avi"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.output_var.set(filename)
    
    def auto_generate_output_name(self):
        """Tự động tạo tên file output dựa trên image"""
        if self.image_var.get():
            image_path = Path(self.image_var.get())
            base_name = image_path.stem
            output_name = f"output/{base_name}_video_{self.job_id:03d}.mp4"
            self.output_var.set(output_name)
    
    def duplicate_job(self):
        """Duplicate job này"""
        if self.on_change:
            self.on_change('duplicate', self)
    
    def delete_job(self):
        """Xóa job này"""
        if self.on_delete:
            self.on_delete(self)
    
    def on_field_change(self, *args):
        """Callback khi có thay đổi trong fields"""
        self.update_status_indicators()
        if self.on_change:
            self.on_change('update', self)
    
    def update_status_indicators(self):
        """Cập nhật các indicator trạng thái"""
        # Check image
        if self.image_var.get() and os.path.exists(self.image_var.get()):
            self.image_status.config(text="✅")
        else:
            self.image_status.config(text="❌")
        
        # Check audio
        if self.audio_var.get() and os.path.exists(self.audio_var.get()):
            self.audio_status.config(text="✅")
        else:
            self.audio_status.config(text="❌")
        
        # Update overall status
        if self.is_ready():
            self.status_var.set("Ready")
        else:
            self.status_var.set("Incomplete")
    
    def is_ready(self):
        """Kiểm tra job có sẵn sàng render không"""
        return (self.image_var.get() and os.path.exists(self.image_var.get()) and
                self.audio_var.get() and os.path.exists(self.audio_var.get()) and
                self.output_var.get())
    
    def get_render_job(self):
        """Tạo RenderJob object"""
        if self.is_ready():
            return RenderJob(
                id=f"job_{self.job_id:03d}",
                image_path=self.image_var.get(),
                audio_path=self.audio_var.get(),
                output_path=self.output_var.get()
            )
        return None
    
    def update_progress(self, status, progress=0):
        """Cập nhật progress và status"""
        self.status_var.set(status)
        self.progress_bar['value'] = progress
        
        # Update frame color based on status
        if status == "completed":
            self.frame.config(text=f"📋 Job {self.job_id:03d} ✅")
        elif status == "failed":
            self.frame.config(text=f"📋 Job {self.job_id:03d} ❌")
        elif status == "processing":
            self.frame.config(text=f"📋 Job {self.job_id:03d} 🔄")
        else:
            self.frame.config(text=f"📋 Job {self.job_id:03d}")


class FinaleGUI:
    """Main GUI cho Finale - Dynamic Multi-Job Video Renderer"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🎬 FINALE - Dynamic Multi-Job Video Renderer")
        self.root.geometry("1000x700")
        
        # Data
        self.jobs = []  # List of JobWidget objects
        self.job_counter = 0
        self.batch_renderer = BatchRenderer()
        self.batch_renderer.set_progress_callback(self.on_progress_update)
        self.is_rendering = False
        
        self.setup_ui()
        self.add_new_job()  # Bắt đầu với 1 job
    
    def setup_ui(self):
        """Setup main UI"""
        # Title
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="🎬 FINALE - Dynamic Multi-Job Video Renderer", 
                               font=("Arial", 16, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="Add jobs dynamically • Render up to 100+ videos", 
                                  font=("Arial", 10))
        subtitle_label.pack()
        
        # Main content area with scrolling
        self.setup_scrollable_area()
        
        # Control buttons
        self.setup_control_buttons()
        
        # Progress area
        self.setup_progress_area()
    
    def setup_scrollable_area(self):
        """Setup scrollable area cho jobs"""
        # Canvas và scrollbar
        canvas_frame = ttk.Frame(self.root)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.canvas = tk.Canvas(canvas_frame, bg='white')
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Jobs container
        self.jobs_frame = ttk.Frame(self.scrollable_frame)
        self.jobs_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Mouse wheel scrolling
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)
    
    def setup_control_buttons(self):
        """Setup control buttons"""
        controls_frame = ttk.Frame(self.root)
        controls_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Left side buttons
        left_frame = ttk.Frame(controls_frame)
        left_frame.pack(side=tk.LEFT)
        
        ttk.Button(left_frame, text="➕ Add Job", 
                  command=self.add_new_job).pack(side=tk.LEFT, padx=2)
        ttk.Button(left_frame, text="📁 Batch Select Images", 
                  command=self.batch_select_images).pack(side=tk.LEFT, padx=2)
        ttk.Button(left_frame, text="🗑️ Clear All", 
                  command=self.clear_all_jobs).pack(side=tk.LEFT, padx=2)
        
        # Right side buttons
        right_frame = ttk.Frame(controls_frame)
        right_frame.pack(side=tk.RIGHT)
        
        self.render_button = ttk.Button(right_frame, text="🚀 Start All Jobs", 
                                       command=self.start_batch_render)
        self.render_button.pack(side=tk.RIGHT, padx=2)
        
        self.stop_button = ttk.Button(right_frame, text="⏹️ Stop", 
                                     command=self.stop_render, state='disabled')
        self.stop_button.pack(side=tk.RIGHT, padx=2)
        
        ttk.Button(right_frame, text="⚙️ Settings", 
                  command=self.show_settings).pack(side=tk.RIGHT, padx=2)
    
    def setup_progress_area(self):
        """Setup progress tracking area"""
        progress_frame = ttk.LabelFrame(self.root, text="📊 Overall Progress", padding="10")
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Progress info
        info_frame = ttk.Frame(progress_frame)
        info_frame.pack(fill=tk.X, pady=2)
        
        self.progress_info_var = tk.StringVar(value="0 jobs ready")
        ttk.Label(info_frame, textvariable=self.progress_info_var).pack(side=tk.LEFT)
        
        self.eta_var = tk.StringVar(value="")
        ttk.Label(info_frame, textvariable=self.eta_var).pack(side=tk.RIGHT)
        
        # Progress bar
        self.overall_progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.overall_progress_bar.pack(fill=tk.X, pady=5)
        
        # Current job info
        self.current_job_var = tk.StringVar(value="No job running")
        ttk.Label(progress_frame, textvariable=self.current_job_var).pack()
    
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def add_new_job(self):
        """Thêm job mới"""
        self.job_counter += 1
        job_widget = JobWidget(
            parent_frame=self.jobs_frame,
            job_id=self.job_counter,
            on_delete_callback=self.delete_job,
            on_change_callback=self.on_job_change
        )
        
        self.jobs.append(job_widget)
        self.update_progress_info()
        
        # Auto scroll to bottom
        self.root.after(100, lambda: self.canvas.yview_moveto(1.0))
    
    def delete_job(self, job_widget):
        """Xóa job"""
        if len(self.jobs) > 1:  # Giữ ít nhất 1 job
            job_widget.frame.destroy()
            self.jobs.remove(job_widget)
            self.update_progress_info()
        else:
            messagebox.showwarning("Warning", "Must keep at least one job!")
    
    def on_job_change(self, action, job_widget):
        """Handle job changes"""
        if action == 'duplicate':
            self.duplicate_job(job_widget)
        elif action == 'update':
            self.update_progress_info()
    
    def duplicate_job(self, source_job):
        """Duplicate một job"""
        new_job = self.add_new_job()
        if new_job:
            # Copy settings from source
            new_job.image_var.set(source_job.image_var.get())
            new_job.audio_var.set(source_job.audio_var.get())
            # Auto-generate new output name
            new_job.auto_generate_output_name()
    
    def batch_select_images(self):
        """Chọn nhiều ảnh cùng lúc và tạo jobs"""
        files = filedialog.askopenfilenames(
            title="Select multiple images",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
                ("All files", "*.*")
            ]
        )
        
        if files:
            # Clear existing jobs first
            if messagebox.askyesno("Confirm", f"Create {len(files)} new jobs? This will clear existing jobs."):
                self.clear_all_jobs()
                
                for file_path in files:
                    job = self.add_new_job()
                    job.image_var.set(file_path)
                    job.auto_generate_output_name()
    
    def clear_all_jobs(self):
        """Xóa tất cả jobs"""
        if self.jobs and messagebox.askyesno("Confirm", "Clear all jobs?"):
            for job in self.jobs[:]:  # Copy list to avoid modification during iteration
                job.frame.destroy()
            self.jobs.clear()
            self.job_counter = 0
            self.add_new_job()  # Add one default job
    
    def update_progress_info(self):
        """Cập nhật thông tin progress"""
        total_jobs = len(self.jobs)
        ready_jobs = sum(1 for job in self.jobs if job.is_ready())
        
        self.progress_info_var.set(f"{ready_jobs}/{total_jobs} jobs ready")
        
        if total_jobs > 0:
            progress = (ready_jobs / total_jobs) * 100
            self.overall_progress_bar['value'] = progress
    
    def start_batch_render(self):
        """Bắt đầu render tất cả jobs"""
        if self.is_rendering:
            messagebox.showwarning("Warning", "Already rendering!")
            return
        
        # Collect ready jobs
        ready_jobs = [job.get_render_job() for job in self.jobs if job.get_render_job()]
        
        if not ready_jobs:
            messagebox.showwarning("Warning", "No jobs ready to render!")
            return
        
        # Confirm start
        if not messagebox.askyesno("Confirm", f"Start rendering {len(ready_jobs)} jobs?"):
            return
        
        # Add jobs to batch renderer
        for render_job in ready_jobs:
            self.batch_renderer.add_job(render_job)
        
        # Update UI
        self.is_rendering = True
        self.render_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        # Start rendering
        self.batch_renderer.start_processing_async()
    
    def stop_render(self):
        """Dừng rendering"""
        self.batch_renderer.stop_processing()
        self.is_rendering = False
        self.render_button.config(state='normal')
        self.stop_button.config(state='disabled')
    
    def on_progress_update(self, job, status):
        """Callback khi có update từ batch renderer"""
        self.root.after(0, lambda: self.update_job_progress(job, status))
    
    def update_job_progress(self, job, status):
        """Update progress cho job cụ thể"""
        # Find corresponding job widget
        job_widget = None
        for widget in self.jobs:
            if widget.get_render_job() and widget.get_render_job().id == job.id:
                job_widget = widget
                break
        
        if job_widget:
            if job.status == "completed":
                job_widget.update_progress("Completed ✅", 100)
            elif job.status == "failed":
                job_widget.update_progress(f"Failed: {job.error_message}", 0)
            elif job.status == "processing":
                job_widget.update_progress("Processing... 🔄", 50)
        
        # Update overall progress
        total = status['pending'] + status['completed'] + status['failed']
        if total > 0:
            completed = status['completed'] + status['failed']
            progress = (completed / total) * 100
            self.overall_progress_bar['value'] = progress
            
            self.progress_info_var.set(f"{completed}/{total} jobs processed")
            
            if status['current']:
                self.current_job_var.set(f"Current: {status['current']}")
            else:
                self.current_job_var.set("No job running")
        
        # Check if finished
        if not status['is_running'] and self.is_rendering:
            self.is_rendering = False
            self.render_button.config(state='normal')
            self.stop_button.config(state='disabled')
            
            messagebox.showinfo("Completed", 
                               f"Batch rendering completed!\n"
                               f"Completed: {status['completed']}\n"
                               f"Failed: {status['failed']}")
    
    def show_settings(self):
        """Hiển thị settings dialog"""
        messagebox.showinfo("Settings", "Settings dialog will be implemented in future version!")


def main():
    """Main function"""
    root = tk.Tk()
    app = FinaleGUI(root)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()


if __name__ == "__main__":
    main()
