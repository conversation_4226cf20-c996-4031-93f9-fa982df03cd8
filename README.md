# Video Renderer Tool

Tool chuyên dụng để render ảnh và nhạc thành video đơn giản với Python.

## Tính năng

- ✅ Kết hợp 1 ảnh tĩnh với 1 file audio thành video
- ✅ Hỗ trợ nhiều định dạng ảnh: JPG, PNG, BMP, TIFF, WebP
- ✅ Hỗ trợ nhiều định dạng audio: MP3, WAV, AAC, M4A, FLAC, OGG
- ✅ Tự động resize ảnh để phù hợp với kích thước video
- ✅ Giữ nguyên tỷ lệ ảnh (aspect ratio)
- ✅ Hỗ trợ video dài (3+ tiếng)
- ✅ Giao diện dòng lệnh (CLI)
- ✅ Giao diện đồ họa (GUI)

## Cài đặt

### 1. Cài đặt Python dependencies

```bash
pip install -r requirements.txt
```

### 2. Cài đặt FFmpeg (bắt buộc)

**Windows:**
- Tải FFmpeg từ: https://ffmpeg.org/download.html
- <PERSON><PERSON><PERSON>i nén và thêm vào PATH

**macOS:**
```bash
brew install ffmpeg
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ffmpeg
```

## Sử dụng

### Giao diện dòng lệnh (CLI)

```bash
# Cách sử dụng cơ bản
python video_renderer.py image.jpg audio.mp3

# Chỉ định file đầu ra
python video_renderer.py image.jpg audio.mp3 -o my_video.mp4

# Tùy chỉnh kích thước video
python video_renderer.py image.jpg audio.mp3 -w 1280 -h 720
```

**Tham số:**
- `image`: Đường dẫn đến file ảnh
- `audio`: Đường dẫn đến file audio
- `-o, --output`: File video đầu ra (mặc định: output_video.mp4)
- `-w, --width`: Chiều rộng video (mặc định: 1920)
- `-h, --height`: Chiều cao video (mặc định: 1080)

### Giao diện đồ họa (GUI)

```bash
python gui_video_renderer.py
```

Giao diện GUI cho phép:
- Chọn file ảnh và audio bằng cách click
- Tùy chỉnh kích thước video
- Theo dõi tiến trình render
- Hiển thị thông báo kết quả

## Ví dụ

### CLI Examples

```bash
# Render video Full HD
python video_renderer.py photo.jpg song.mp3 -o vacation_video.mp4

# Render video HD 720p
python video_renderer.py photo.png music.wav -w 1280 -h 720 -o hd_video.mp4

# Render với file audio dài 3 tiếng
python video_renderer.py background.jpg long_audio.mp3 -o long_video.mp4
```

### Định dạng được hỗ trợ

**Ảnh:**
- JPG/JPEG
- PNG
- BMP
- TIFF
- WebP

**Audio:**
- MP3
- WAV
- AAC
- M4A
- FLAC
- OGG

**Video đầu ra:**
- MP4 (khuyến nghị)
- AVI

## Lưu ý kỹ thuật

- **FPS thấp**: Tool sử dụng FPS = 1 vì chỉ render ảnh tĩnh, giúp giảm kích thước file
- **Codec**: Sử dụng H.264 cho video và AAC cho audio để tương thích tốt
- **Memory**: Tool tối ưu để xử lý file audio dài mà không gây quá tải RAM
- **Aspect Ratio**: Tự động giữ nguyên tỷ lệ ảnh, thêm viền đen nếu cần

## Xử lý lỗi

Tool sẽ kiểm tra và báo lỗi cho các trường hợp:
- File không tồn tại
- Định dạng file không được hỗ trợ
- Lỗi codec/FFmpeg
- Không đủ dung lượng đĩa
- File audio bị lỗi

## Performance

- **File nhỏ**: < 1 phút render
- **File 3 tiếng**: 5-15 phút render (tùy thuộc cấu hình máy)
- **RAM usage**: ~200-500MB
- **Disk space**: Video output thường ~1-5MB/phút

## Troubleshooting

**Lỗi FFmpeg:**
```
ImageIO cannot find ffmpeg
```
→ Cài đặt FFmpeg và thêm vào PATH

**Lỗi codec:**
```
codec not found
```
→ Cập nhật FFmpeg lên phiên bản mới nhất

**Lỗi memory:**
```
MemoryError
```
→ Giảm kích thước video hoặc tăng RAM
