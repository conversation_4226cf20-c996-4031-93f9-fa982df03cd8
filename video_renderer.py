#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tool để render ảnh và nhạc thành video
Tạo video với 1 ảnh tĩnh và đoạn nhạc xuyên suốt
"""

import os
import sys
from pathlib import Path
from moviepy import ImageClip, AudioFileClip, CompositeVideoClip
from PIL import Image
import argparse

# Fix encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())


class VideoRenderer:
    def __init__(self):
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        self.supported_audio_formats = ['.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg']
    
    def validate_files(self, image_path, audio_path):
        """Kiểm tra tính hợp lệ của file ảnh và audio"""
        # Kiểm tra file ảnh
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"File ảnh không tồn tại: {image_path}")
        
        image_ext = Path(image_path).suffix.lower()
        if image_ext not in self.supported_image_formats:
            raise ValueError(f"Định dạng ảnh không được hỗ trợ: {image_ext}")
        
        # Kiểm tra file audio
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"File audio không tồn tại: {audio_path}")
        
        audio_ext = Path(audio_path).suffix.lower()
        if audio_ext not in self.supported_audio_formats:
            raise ValueError(f"Định dạng audio không được hỗ trợ: {audio_ext}")
    
    def get_audio_duration(self, audio_path):
        """Lấy thời lượng của file audio"""
        try:
            audio_clip = AudioFileClip(audio_path)
            duration = audio_clip.duration
            audio_clip.close()
            return duration
        except Exception as e:
            raise Exception(f"Không thể đọc file audio: {e}")
    
    def resize_image_to_video_size(self, image_path, target_width=1920, target_height=1080):
        """Resize ảnh để phù hợp với kích thước video"""
        try:
            with Image.open(image_path) as img:
                # Chuyển sang RGB nếu cần
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Tính toán tỷ lệ để giữ nguyên aspect ratio
                img_width, img_height = img.size
                target_ratio = target_width / target_height
                img_ratio = img_width / img_height
                
                if img_ratio > target_ratio:
                    # Ảnh rộng hơn, fit theo chiều cao
                    new_height = target_height
                    new_width = int(target_height * img_ratio)
                else:
                    # Ảnh cao hơn, fit theo chiều rộng
                    new_width = target_width
                    new_height = int(target_width / img_ratio)
                
                # Resize ảnh
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Tạo background đen và paste ảnh vào giữa
                background = Image.new('RGB', (target_width, target_height), (0, 0, 0))
                paste_x = (target_width - new_width) // 2
                paste_y = (target_height - new_height) // 2
                background.paste(resized_img, (paste_x, paste_y))
                
                # Lưu ảnh tạm
                temp_image_path = "temp_resized_image.jpg"
                background.save(temp_image_path, "JPEG", quality=95)
                
                return temp_image_path
        except Exception as e:
            raise Exception(f"Không thể xử lý ảnh: {e}")
    
    def render_video(self, image_path, audio_path, output_path, video_width=1920, video_height=1080):
        """Render video từ ảnh và audio"""
        try:
            print("Checking files...")
            self.validate_files(image_path, audio_path)

            print("Getting audio info...")
            audio_duration = self.get_audio_duration(audio_path)
            print(f"Audio duration: {audio_duration:.2f} seconds ({audio_duration/3600:.2f} hours)")

            print("Processing image...")
            processed_image_path = self.resize_image_to_video_size(
                image_path, video_width, video_height
            )

            print("Creating video clip...")
            # Tạo image clip với thời lượng bằng audio
            image_clip = ImageClip(processed_image_path, duration=audio_duration)

            print("Loading audio...")
            # Load audio clip
            audio_clip = AudioFileClip(audio_path)

            print("Combining video and audio...")
            # Kết hợp video và audio
            final_video = image_clip.with_audio(audio_clip)

            print(f"Rendering video: {output_path}")
            print("This may take a few minutes...")
            
            # Render video với các settings tối ưu
            final_video.write_videofile(
                output_path,
                fps=1,  # FPS thấp vì chỉ là ảnh tĩnh
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True
            )
            
            # Dọn dẹp
            image_clip.close()
            audio_clip.close()
            final_video.close()
            
            # Xóa file ảnh tạm
            if os.path.exists(processed_image_path):
                os.remove(processed_image_path)

            print(f"SUCCESS! Video saved at: {output_path}")

        except Exception as e:
            print(f"ERROR rendering video: {e}")
            # Dọn dẹp file tạm nếu có lỗi
            if 'processed_image_path' in locals() and os.path.exists(processed_image_path):
                os.remove(processed_image_path)
            raise


def main():
    parser = argparse.ArgumentParser(description='Tool render ảnh và nhạc thành video')
    parser.add_argument('image', help='Đường dẫn đến file ảnh')
    parser.add_argument('audio', help='Đường dẫn đến file audio')
    parser.add_argument('-o', '--output', default='output_video.mp4', 
                       help='Đường dẫn file video đầu ra (mặc định: output_video.mp4)')
    parser.add_argument('-w', '--width', type=int, default=1920,
                       help='Chiều rộng video (mặc định: 1920)')
    parser.add_argument('--height', type=int, default=1080,
                       help='Chiều cao video (mặc định: 1080)')
    
    args = parser.parse_args()
    
    renderer = VideoRenderer()
    
    try:
        renderer.render_video(
            image_path=args.image,
            audio_path=args.audio,
            output_path=args.output,
            video_width=args.width,
            video_height=args.height
        )
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
