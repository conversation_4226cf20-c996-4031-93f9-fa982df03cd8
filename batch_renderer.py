#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch Video Renderer - Render nhiều video theo thứ tự
Hỗ trợ import từ CSV, JSON và folder
"""

import os
import sys
import csv
import json
import time
import threading
from pathlib import Path
from datetime import datetime
from queue import Queue
from dataclasses import dataclass
from typing import List, Optional, Callable
from video_renderer import VideoRenderer

# Fix encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())


@dataclass
class RenderJob:
    """Đại diện cho một job render video"""
    id: str
    image_path: str
    audio_path: str
    output_path: str
    width: int = 1920
    height: int = 1080
    status: str = "pending"  # pending, processing, completed, failed
    error_message: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    @property
    def duration(self) -> Optional[float]:
        """Thời gian render (giây)"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


class BatchRenderer:
    """Batch renderer cho nhiều video"""
    
    def __init__(self, max_workers: int = 1):
        self.renderer = VideoRenderer()
        self.job_queue = Queue()
        self.completed_jobs = []
        self.failed_jobs = []
        self.current_job = None
        self.is_running = False
        self.max_workers = max_workers
        self.progress_callback: Optional[Callable] = None
        self.log_file = None
        
    def add_job(self, job: RenderJob):
        """Thêm job vào queue"""
        self.job_queue.put(job)
        self.log(f"Added job: {job.id}")
    
    def add_jobs_from_csv(self, csv_path: str):
        """Import jobs từ file CSV"""
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for i, row in enumerate(reader):
                    job = RenderJob(
                        id=row.get('id', f"job_{i+1}"),
                        image_path=row['image_path'],
                        audio_path=row['audio_path'],
                        output_path=row['output_path'],
                        width=int(row.get('width', 1920)),
                        height=int(row.get('height', 1080))
                    )
                    self.add_job(job)
            self.log(f"Imported {reader.line_num - 1} jobs from CSV: {csv_path}")
        except Exception as e:
            self.log(f"Error importing CSV: {e}")
            raise
    
    def add_jobs_from_json(self, json_path: str):
        """Import jobs từ file JSON"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            jobs_data = data if isinstance(data, list) else data.get('jobs', [])
            
            for i, job_data in enumerate(jobs_data):
                job = RenderJob(
                    id=job_data.get('id', f"job_{i+1}"),
                    image_path=job_data['image_path'],
                    audio_path=job_data['audio_path'],
                    output_path=job_data['output_path'],
                    width=job_data.get('width', 1920),
                    height=job_data.get('height', 1080)
                )
                self.add_job(job)
            self.log(f"Imported {len(jobs_data)} jobs from JSON: {json_path}")
        except Exception as e:
            self.log(f"Error importing JSON: {e}")
            raise
    
    def add_jobs_from_folder(self, folder_path: str, output_folder: str = "output"):
        """Import jobs từ folder (auto-pair image + audio)"""
        try:
            folder = Path(folder_path)
            output_dir = Path(output_folder)
            output_dir.mkdir(exist_ok=True)
            
            # Tìm tất cả file ảnh
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
            audio_extensions = {'.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg'}
            
            images = [f for f in folder.iterdir() if f.suffix.lower() in image_extensions]
            audios = [f for f in folder.iterdir() if f.suffix.lower() in audio_extensions]
            
            # Auto-pair dựa trên tên file
            paired_jobs = []
            for image in images:
                # Tìm audio có tên tương tự
                image_stem = image.stem
                matching_audio = None
                
                for audio in audios:
                    if audio.stem == image_stem:
                        matching_audio = audio
                        break
                
                if matching_audio:
                    job_id = f"auto_{len(paired_jobs)+1}_{image_stem}"
                    output_path = output_dir / f"{image_stem}.mp4"
                    
                    job = RenderJob(
                        id=job_id,
                        image_path=str(image),
                        audio_path=str(matching_audio),
                        output_path=str(output_path)
                    )
                    paired_jobs.append(job)
                    self.add_job(job)
            
            self.log(f"Auto-paired {len(paired_jobs)} jobs from folder: {folder_path}")
            return paired_jobs
            
        except Exception as e:
            self.log(f"Error importing from folder: {e}")
            raise
    
    def set_progress_callback(self, callback: Callable):
        """Set callback để theo dõi tiến trình"""
        self.progress_callback = callback
    
    def set_log_file(self, log_path: str):
        """Set file log"""
        self.log_file = log_path
    
    def log(self, message: str):
        """Ghi log"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        print(log_message)
        
        if self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + "\n")
    
    def get_queue_status(self):
        """Lấy trạng thái queue"""
        return {
            'pending': self.job_queue.qsize(),
            'completed': len(self.completed_jobs),
            'failed': len(self.failed_jobs),
            'current': self.current_job.id if self.current_job else None,
            'is_running': self.is_running
        }
    
    def process_single_job(self, job: RenderJob):
        """Xử lý một job"""
        try:
            job.status = "processing"
            job.start_time = datetime.now()
            self.current_job = job
            
            self.log(f"Starting job: {job.id}")
            self.log(f"  Image: {job.image_path}")
            self.log(f"  Audio: {job.audio_path}")
            self.log(f"  Output: {job.output_path}")
            
            # Render video
            self.renderer.render_video(
                image_path=job.image_path,
                audio_path=job.audio_path,
                output_path=job.output_path,
                video_width=job.width,
                video_height=job.height
            )
            
            job.status = "completed"
            job.end_time = datetime.now()
            self.completed_jobs.append(job)
            
            self.log(f"Completed job: {job.id} in {job.duration:.2f}s")
            
            if self.progress_callback:
                self.progress_callback(job, self.get_queue_status())
                
        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            job.end_time = datetime.now()
            self.failed_jobs.append(job)
            
            self.log(f"Failed job: {job.id} - Error: {e}")
            
            if self.progress_callback:
                self.progress_callback(job, self.get_queue_status())
        
        finally:
            self.current_job = None
    
    def start_processing(self):
        """Bắt đầu xử lý queue"""
        if self.is_running:
            self.log("Batch processing already running!")
            return
        
        self.is_running = True
        total_jobs = self.job_queue.qsize()
        self.log(f"Starting batch processing: {total_jobs} jobs")
        
        start_time = datetime.now()
        
        try:
            while not self.job_queue.empty():
                job = self.job_queue.get()
                self.process_single_job(job)
                
                # Progress update
                remaining = self.job_queue.qsize()
                completed = len(self.completed_jobs)
                failed = len(self.failed_jobs)
                
                self.log(f"Progress: {completed + failed}/{total_jobs} "
                        f"(Completed: {completed}, Failed: {failed}, Remaining: {remaining})")
        
        finally:
            self.is_running = False
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()
            
            self.log(f"Batch processing completed in {total_duration:.2f}s")
            self.log(f"Results: {len(self.completed_jobs)} completed, {len(self.failed_jobs)} failed")
    
    def start_processing_async(self):
        """Bắt đầu xử lý queue trong background thread"""
        if self.is_running:
            self.log("Batch processing already running!")
            return
        
        thread = threading.Thread(target=self.start_processing, daemon=True)
        thread.start()
        return thread
    
    def stop_processing(self):
        """Dừng xử lý (sau khi hoàn thành job hiện tại)"""
        self.is_running = False
        self.log("Stop signal sent. Will stop after current job completes.")
    
    def export_results(self, output_path: str):
        """Export kết quả ra file JSON"""
        results = {
            'summary': {
                'total_jobs': len(self.completed_jobs) + len(self.failed_jobs),
                'completed': len(self.completed_jobs),
                'failed': len(self.failed_jobs),
                'export_time': datetime.now().isoformat()
            },
            'completed_jobs': [
                {
                    'id': job.id,
                    'image_path': job.image_path,
                    'audio_path': job.audio_path,
                    'output_path': job.output_path,
                    'duration': job.duration,
                    'start_time': job.start_time.isoformat() if job.start_time else None,
                    'end_time': job.end_time.isoformat() if job.end_time else None
                }
                for job in self.completed_jobs
            ],
            'failed_jobs': [
                {
                    'id': job.id,
                    'image_path': job.image_path,
                    'audio_path': job.audio_path,
                    'output_path': job.output_path,
                    'error_message': job.error_message,
                    'start_time': job.start_time.isoformat() if job.start_time else None,
                    'end_time': job.end_time.isoformat() if job.end_time else None
                }
                for job in self.failed_jobs
            ]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        self.log(f"Results exported to: {output_path}")


def main():
    """Demo batch renderer"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Batch Video Renderer')
    parser.add_argument('--csv', help='Import jobs from CSV file')
    parser.add_argument('--json', help='Import jobs from JSON file')
    parser.add_argument('--folder', help='Import jobs from folder (auto-pair)')
    parser.add_argument('--output-folder', default='batch_output', 
                       help='Output folder for batch processing')
    parser.add_argument('--log', help='Log file path')
    parser.add_argument('--results', default='batch_results.json',
                       help='Results export file')
    
    args = parser.parse_args()
    
    # Tạo batch renderer
    batch_renderer = BatchRenderer()
    
    if args.log:
        batch_renderer.set_log_file(args.log)
    
    # Import jobs
    if args.csv:
        batch_renderer.add_jobs_from_csv(args.csv)
    elif args.json:
        batch_renderer.add_jobs_from_json(args.json)
    elif args.folder:
        batch_renderer.add_jobs_from_folder(args.folder, args.output_folder)
    else:
        print("Please specify input source: --csv, --json, or --folder")
        return
    
    # Bắt đầu processing
    batch_renderer.start_processing()
    
    # Export results
    batch_renderer.export_results(args.results)


if __name__ == "__main__":
    main()
